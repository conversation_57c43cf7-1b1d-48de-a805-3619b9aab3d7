"""
Login view for user authentication.
"""
import flet as ft
from gui.config.constants import ROUTE_DASHBOARD, ROUTE_ADMIN
from gui.config.language import get_text
from gui.services.auth_service import authenticate_user


def create_login_view(page: ft.Page):
    """Create the login page with username and password fields."""
    is_mobile = getattr(page, 'is_mobile', False)
    current_language = getattr(page, 'language', 'fr')

    # Username field
    username_field = ft.TextField(
        label=get_text("username", current_language),
        width=300,
        prefix_icon=ft.Icons.PERSON,
        border_radius=10,
        autofocus=True
    )

    # Password field
    password_field = ft.TextField(
        label=get_text("password", current_language),
        width=300,
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True,
        border_radius=10,
        on_submit=lambda _: login_clicked(None)
    )

    # Error message
    error_message = ft.Text(
        "",
        color=ft.Colors.RED,
        size=14,
        visible=False
    )

    # Loading indicator
    loading_indicator = ft.ProgressRing(
        width=20,
        height=20,
        visible=False
    )

    def show_error(message):
        """Show error message."""
        error_message.value = message
        error_message.visible = True
        page.update()

    def hide_error():
        """Hide error message."""
        error_message.visible = False
        page.update()

    def show_loading(show=True):
        """Show or hide loading indicator."""
        loading_indicator.visible = show
        login_button.disabled = show
        page.update()

    def login_clicked(e):
        """Handle login button click."""
        hide_error()

        username = username_field.value.strip()
        password = password_field.value

        if not username:
            show_error(get_text("please_enter_username", current_language))
            return

        if not password:
            show_error(get_text("please_enter_password", current_language))
            return

        show_loading(True)

        # Authenticate user
        user = authenticate_user(username, password)

        show_loading(False)

        if user:
            # Store user information in page state
            page.app_state.current_user = user
            page.app_state.is_authenticated = True

            # Redirect based on user type
            if user['user_type'] == 'admin':
                page.go(ROUTE_ADMIN)
            else:
                page.go(ROUTE_DASHBOARD)
        else:
            show_error(get_text("invalid_credentials", current_language))

    # Login button
    login_button = ft.ElevatedButton(
        text=get_text("login", current_language),
        icon=ft.Icons.LOGIN,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            padding=ft.padding.symmetric(horizontal=32, vertical=16),
            text_style=ft.TextStyle(
                size=16,
                weight=ft.FontWeight.W_500
            )
        ),
        on_click=login_clicked,
        width=300
    )

    # App logo/icon
    logo_container = ft.Container(
        content=ft.Icon(
            ft.Icons.SCHOOL,
            size=80,
            color=ft.Colors.BLUE_600
        ),
        margin=ft.margin.only(bottom=20)
    )

    # App title
    app_title = ft.Text(
        get_text("app_name", current_language),
        size=32,
        weight=ft.FontWeight.BOLD,
        color=ft.Colors.BLUE_900,
        text_align=ft.TextAlign.CENTER
    )

    # Login form container
    login_form = ft.Container(
        content=ft.Column([
            logo_container,
            app_title,
            ft.Container(height=30),
            username_field,
            ft.Container(height=15),
            password_field,
            ft.Container(height=10),
            error_message,
            ft.Container(height=20),
            ft.Row([
                loading_indicator,
                login_button
            ], alignment=ft.MainAxisAlignment.CENTER),
        ],
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=0),
        padding=ft.padding.all(40),
        bgcolor=ft.Colors.WHITE,
        border_radius=15,
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=15,
            color=ft.Colors.BLUE_GREY_100,
            offset=ft.Offset(0, 5)
        ),
        width=400 if not is_mobile else page.width * 0.9
    )

    # Main content container
    main_content = ft.Container(
        content=login_form,
        alignment=ft.alignment.center,
        expand=True
    )

    # Gradient background container
    background_container = ft.Container(
        content=main_content,
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_center,
            end=ft.alignment.bottom_center,
            colors=[ft.Colors.BLUE_50, ft.Colors.WHITE, ft.Colors.BLUE_50]
        ),
        expand=True
    )

    # Create the view
    view = ft.View(
        route="/login",
        controls=[background_container],
        padding=0,
        spacing=0
    )

    return view
