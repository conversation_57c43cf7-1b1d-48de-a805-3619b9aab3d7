"""
Breadcrumb navigation component for better navigation context.
"""
import flet as ft
from gui.config.constants import (
    ROUTE_DASHBOARD, ROUTE_CLASSES, ROUTE_QUIZZES, ROUTE_SETTINGS,
    ICON_DASHBOARD, ICON_CLASS, ICON_QUIZ, ICON_SETTINGS
)
from gui.config.language import get_text

def create_breadcrumb(page: ft.Page, current_route: str, custom_items: list = None):
    """
    Create a breadcrumb navigation component.

    Args:
        page: The Flet page object
        current_route: The current route
        custom_items: Optional custom breadcrumb items

    Returns:
        ft.Container: The breadcrumb component
    """
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Route mapping
    route_info = {
        ROUTE_DASHBOARD: {
            'title': get_text("dashboard", current_language),
            'icon': ICON_DASHBOARD,
            'color': ft.Colors.BLUE_600
        },
        ROUTE_CLASSES: {
            'title': get_text("classes", current_language),
            'icon': ICON_CLASS,
            'color': ft.Colors.GREEN_600
        },

        ROUTE_QUIZZES: {
            'title': get_text("quizzes", current_language),
            'icon': ICON_QUIZ,
            'color': ft.Colors.PURPLE_600
        },
        ROUTE_SETTINGS: {
            'title': "Settings",
            'icon': ICON_SETTINGS,
            'color': ft.Colors.GREY_600
        }
    }

    # Build breadcrumb items
    breadcrumb_items = []

    if custom_items:
        # Use custom items if provided
        for i, item in enumerate(custom_items):
            if i > 0:
                breadcrumb_items.append(
                    ft.Icon(ft.Icons.CHEVRON_RIGHT, size=16, color=ft.Colors.GREY_400)
                )

            breadcrumb_items.append(create_breadcrumb_item(
                item.get('title', ''),
                item.get('icon'),
                item.get('route'),
                item.get('color', ft.Colors.GREY_600),
                i == len(custom_items) - 1,  # is_current
                page,
                is_mobile
            ))
    else:
        # Default breadcrumb: Home > Current Section
        # Home breadcrumb
        breadcrumb_items.append(create_breadcrumb_item(
            get_text("dashboard", current_language),
            ICON_DASHBOARD,
            ROUTE_DASHBOARD,
            ft.Colors.BLUE_600,
            current_route == ROUTE_DASHBOARD,
            page,
            is_mobile
        ))

        # Current section (if not dashboard)
        if current_route != ROUTE_DASHBOARD and current_route in route_info:
            breadcrumb_items.append(
                ft.Icon(ft.Icons.CHEVRON_RIGHT, size=16, color=ft.Colors.GREY_400)
            )

            info = route_info[current_route]
            breadcrumb_items.append(create_breadcrumb_item(
                info['title'],
                info['icon'],
                current_route,
                info['color'],
                True,  # is_current
                page,
                is_mobile
            ))

    return ft.Container(
        content=ft.Row(
            breadcrumb_items,
            spacing=8,
            scroll=ft.ScrollMode.AUTO if is_mobile else None
        ),
        padding=ft.padding.symmetric(horizontal=16, vertical=8),
        margin=ft.margin.only(bottom=16),
        bgcolor=ft.Colors.with_opacity(0.05, ft.Colors.GREY),
        border_radius=ft.border_radius.all(8),
        border=ft.border.all(1, ft.Colors.with_opacity(0.1, ft.Colors.GREY))
    )

def create_breadcrumb_item(title, icon, route, color, is_current, page, is_mobile):
    """Create a single breadcrumb item."""
    text_size = 12 if is_mobile else 14
    icon_size = 16 if is_mobile else 18

    content = ft.Row([
        ft.Icon(icon, size=icon_size, color=color if is_current else ft.Colors.GREY_600),
        ft.Text(
            title,
            size=text_size,
            weight=ft.FontWeight.BOLD if is_current else ft.FontWeight.NORMAL,
            color=color if is_current else ft.Colors.GREY_600
        )
    ], spacing=6)

    if is_current:
        # Current item - not clickable
        return ft.Container(
            content=content,
            padding=ft.padding.symmetric(horizontal=8, vertical=4),
            bgcolor=ft.Colors.with_opacity(0.1, color),
            border_radius=ft.border_radius.all(6),
            border=ft.border.all(1, ft.Colors.with_opacity(0.3, color))
        )
    else:
        # Clickable item
        return ft.Container(
            content=content,
            padding=ft.padding.symmetric(horizontal=8, vertical=4),
            border_radius=ft.border_radius.all(6),
            on_click=lambda _: page.go(route) if route else None,
            bgcolor=ft.Colors.TRANSPARENT,
            border=ft.border.all(1, ft.Colors.TRANSPARENT)
        )

def create_page_breadcrumb(page: ft.Page, section_title: str, subsection_title: str = None):
    """
    Create a breadcrumb for a specific page with section and optional subsection.

    Args:
        page: The Flet page object
        section_title: The main section title
        subsection_title: Optional subsection title

    Returns:
        ft.Container: The breadcrumb component
    """
    current_language = getattr(page, 'language', 'en')

    # Build custom breadcrumb items
    items = [
        {
            'title': get_text("dashboard", current_language),
            'icon': ICON_DASHBOARD,
            'route': ROUTE_DASHBOARD,
            'color': ft.Colors.BLUE_600
        },
        {
            'title': section_title,
            'icon': get_section_icon(page.route),
            'route': page.route,
            'color': get_section_color(page.route)
        }
    ]

    if subsection_title:
        items.append({
            'title': subsection_title,
            'icon': ft.Icons.ARROW_FORWARD_IOS,
            'route': None,
            'color': ft.Colors.GREY_600
        })

    return create_breadcrumb(page, page.route, items)

def get_section_icon(route):
    """Get the appropriate icon for a route."""
    icons = {
        ROUTE_CLASSES: ICON_CLASS,
        ROUTE_QUIZZES: ICON_QUIZ,
        ROUTE_SETTINGS: ICON_SETTINGS
    }
    return icons.get(route, ft.Icons.CIRCLE)

def get_section_color(route):
    """Get the appropriate color for a route."""
    colors = {
        ROUTE_CLASSES: ft.Colors.GREEN_600,
        ROUTE_QUIZZES: ft.Colors.PURPLE_600,
        ROUTE_SETTINGS: ft.Colors.GREY_600
    }
    return colors.get(route, ft.Colors.GREY_600)
